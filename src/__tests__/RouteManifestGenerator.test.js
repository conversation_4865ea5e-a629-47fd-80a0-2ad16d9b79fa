const fs = require('fs');
const path = require('path');
const RouteManifestGenerator = require('../RouteManifestGenerator');

describe('RouteManifestGenerator', () => {
  let generator;
  let tempDir;

  beforeEach(() => {
    generator = new RouteManifestGenerator();
    tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up temp files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('addRoutes', () => {
    test('should add routes to the generator', () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller' },
        { path: '/api/posts', method: 'POST', type: 'controller' }
      ];

      generator.addRoutes(routes, 'test');

      expect(generator.routes).toHaveLength(2);
      expect(generator.routes[0].source).toBe('test');
      expect(generator.routes[1].source).toBe('test');
    });

    test('should normalize routes with missing fields', () => {
      const routes = [
        { path: '/incomplete' }, // missing method, type, etc.
        { method: 'POST' } // missing path
      ];

      generator.addRoutes(routes, 'test');

      expect(generator.routes).toHaveLength(2);
      expect(generator.routes[0]).toEqual({
        path: '/incomplete',
        method: 'GET',
        type: 'unknown',
        source: 'test',
        confidence: 0.5,
        metadata: {}
      });
      expect(generator.routes[1]).toEqual({
        path: '/',
        method: 'POST',
        type: 'unknown',
        source: 'test',
        confidence: 0.5,
        metadata: {}
      });
    });
  });

  describe('generateManifest', () => {
    test('should generate complete manifest with metadata', () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9 },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet', confidence: 0.8 }
      ];

      generator.addRoutes(routes, 'test');
      const manifest = generator.generateManifest('/test/project');

      expect(manifest).toHaveProperty('routes');
      expect(manifest).toHaveProperty('metadata');
      expect(manifest.metadata).toHaveProperty('timestamp');
      expect(manifest.metadata).toHaveProperty('sourceProject', '/test/project');
      expect(manifest.metadata).toHaveProperty('stats');
      expect(manifest.routes).toHaveLength(2);
    });
  });

  describe('calculateStats', () => {
    test('should calculate comprehensive statistics', () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9 },
        { path: '/api/posts', method: 'POST', type: 'controller', confidence: 0.8 },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet', confidence: 0.7 },
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9 }, // duplicate
        { path: '/low-confidence', method: 'GET', type: 'unknown', confidence: 0.3 }
      ];

      generator.addRoutes(routes, 'test');
      const stats = generator.calculateStats();

      expect(stats.total).toBe(5);
      expect(stats.byType.controller).toBe(3);
      expect(stats.byType.stylesheet).toBe(1);
      expect(stats.byType.unknown).toBe(1);
      expect(stats.byMethod.GET).toBe(4);
      expect(stats.byMethod.POST).toBe(1);
      expect(stats.byConfidence.high).toBe(3); // >= 0.8 (0.9, 0.8, 0.9)
      expect(stats.byConfidence.medium).toBe(1); // 0.5-0.8 (0.7)
      expect(stats.byConfidence.low).toBe(1); // < 0.5 (0.3)
      expect(stats.duplicates).toBe(1);
      expect(stats.averageConfidence).toBeCloseTo(0.72, 2);
    });
  });

  describe('getRoutes', () => {
    beforeEach(() => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9, source: 'annotation' },
        { path: '/api/posts', method: 'POST', type: 'controller', confidence: 0.8, source: 'annotation' },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet', confidence: 0.7, source: 'filesystem' },
        { path: '/admin/users', method: 'GET', type: 'controller', confidence: 0.6, source: 'web.xml' }
      ];
      generator.addRoutes(routes, 'test');
    });

    test('should filter routes by type', () => {
      const controllerRoutes = generator.getRoutes({ type: 'controller' });
      expect(controllerRoutes).toHaveLength(3);
      expect(controllerRoutes.every(r => r.type === 'controller')).toBe(true);
    });

    test('should filter routes by source', () => {
      const annotationRoutes = generator.getRoutes({ source: 'annotation' });
      expect(annotationRoutes).toHaveLength(2);
      expect(annotationRoutes.every(r => r.source === 'annotation')).toBe(true);
    });

    test('should filter routes by method', () => {
      const getRoutes = generator.getRoutes({ method: 'GET' });
      expect(getRoutes).toHaveLength(3);
      expect(getRoutes.every(r => r.method === 'GET')).toBe(true);
    });

    test('should filter routes by minimum confidence', () => {
      const highConfidenceRoutes = generator.getRoutes({ minConfidence: 0.8 });
      expect(highConfidenceRoutes).toHaveLength(2);
      expect(highConfidenceRoutes.every(r => r.confidence >= 0.8)).toBe(true);
    });

    test('should filter routes by path pattern', () => {
      const apiRoutes = generator.getRoutes({ pathPattern: '^/api' });
      expect(apiRoutes).toHaveLength(2);
      expect(apiRoutes.every(r => r.path.startsWith('/api'))).toBe(true);
    });

    test('should apply multiple filters', () => {
      const filteredRoutes = generator.getRoutes({
        type: 'controller',
        method: 'GET',
        minConfidence: 0.8
      });
      expect(filteredRoutes).toHaveLength(1);
      expect(filteredRoutes[0].path).toBe('/api/users');
    });
  });

  describe('sortRoutes', () => {
    test('should sort routes by path ascending', () => {
      const routes = [
        { path: '/z-last', method: 'GET', type: 'controller' },
        { path: '/a-first', method: 'GET', type: 'controller' },
        { path: '/m-middle', method: 'GET', type: 'controller' }
      ];

      generator.addRoutes(routes, 'test');
      generator.sortRoutes('path', 'asc');

      expect(generator.routes[0].path).toBe('/a-first');
      expect(generator.routes[1].path).toBe('/m-middle');
      expect(generator.routes[2].path).toBe('/z-last');
    });

    test('should sort routes by confidence descending', () => {
      const routes = [
        { path: '/low', method: 'GET', type: 'controller', confidence: 0.3 },
        { path: '/high', method: 'GET', type: 'controller', confidence: 0.9 },
        { path: '/medium', method: 'GET', type: 'controller', confidence: 0.6 }
      ];

      generator.addRoutes(routes, 'test');
      generator.sortRoutes('confidence', 'desc');

      expect(generator.routes[0].confidence).toBe(0.9);
      expect(generator.routes[1].confidence).toBe(0.6);
      expect(generator.routes[2].confidence).toBe(0.3);
    });
  });

  describe('deduplicateRoutes', () => {
    test('should remove duplicates keeping highest confidence', () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.7 },
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9 },
        { path: '/api/posts', method: 'GET', type: 'controller', confidence: 0.8 },
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.6 }
      ];

      generator.addRoutes(routes, 'test');
      generator.deduplicateRoutes();

      expect(generator.routes).toHaveLength(2);
      const usersRoute = generator.routes.find(r => r.path === '/api/users');
      expect(usersRoute.confidence).toBe(0.9);
    });
  });

  describe('exportToFile and importFromFile', () => {
    test('should export and import manifest correctly', async () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9 }
      ];

      generator.addRoutes(routes, 'test');
      
      const outputPath = path.join(tempDir, 'manifest.json');
      await generator.exportToFile(outputPath, '/test/project');

      expect(fs.existsSync(outputPath)).toBe(true);

      // Import into new generator
      const newGenerator = new RouteManifestGenerator();
      const imported = await newGenerator.importFromFile(outputPath);

      expect(imported.routes).toHaveLength(1);
      expect(imported.routes[0].path).toBe('/api/users');
      expect(imported.metadata.sourceProject).toBe('/test/project');
    });
  });

  describe('generateSummary', () => {
    test('should generate human-readable summary', () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller', confidence: 0.9 },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet', confidence: 0.8 }
      ];

      generator.addRoutes(routes, 'test');
      const summary = generator.generateSummary();

      expect(summary).toContain('Route Discovery Summary');
      expect(summary).toContain('Total Routes: 2');
      expect(summary).toContain('controller: 1');
      expect(summary).toContain('stylesheet: 1');
      expect(summary).toContain('GET: 2');
    });
  });

  describe('clear', () => {
    test('should clear all routes and metadata', () => {
      const routes = [
        { path: '/api/users', method: 'GET', type: 'controller' }
      ];

      generator.addRoutes(routes, 'test');
      expect(generator.routes).toHaveLength(1);

      generator.clear();
      expect(generator.routes).toHaveLength(0);
      expect(generator.metadata.timestamp).toBeNull();
      expect(generator.metadata.sourceProject).toBeNull();
    });
  });
});
