const fs = require('fs');
const path = require('path');
const RouteDiscovery = require('../RouteDiscovery');

describe('RouteDiscovery', () => {
  let discovery;
  let tempDir;

  beforeEach(() => {
    discovery = new RouteDiscovery();
    tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up temp files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('constructor', () => {
    test('should initialize with default options', () => {
      const defaultDiscovery = new RouteDiscovery();
      expect(defaultDiscovery.options.includeStaticResources).toBe(true);
      expect(defaultDiscovery.options.includeWebXml).toBe(true);
      expect(defaultDiscovery.options.includeAnnotations).toBe(true);
      expect(defaultDiscovery.options.deduplicateRoutes).toBe(true);
    });

    test('should accept custom options', () => {
      const customDiscovery = new RouteDiscovery({
        includeStaticResources: false,
        minConfidence: 0.8
      });
      expect(customDiscovery.options.includeStaticResources).toBe(false);
      expect(customDiscovery.options.minConfidence).toBe(0.8);
    });
  });

  describe('discoverRoutes', () => {
    test('should discover routes from a complete project structure', async () => {
      // Create a mock project structure
      const projectPath = tempDir;
      const webappPath = path.join(projectPath, 'src/main/webapp');
      const javaPath = path.join(projectPath, 'src/main/java');
      const webInfPath = path.join(webappPath, 'WEB-INF');
      
      fs.mkdirSync(webInfPath, { recursive: true });
      fs.mkdirSync(javaPath, { recursive: true });
      fs.mkdirSync(path.join(webappPath, 'css'), { recursive: true });

      // Create web.xml
      const webXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<web-app>
  <servlet>
    <servlet-name>TestServlet</servlet-name>
    <servlet-class>com.example.TestServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>TestServlet</servlet-name>
    <url-pattern>/test/*</url-pattern>
  </servlet-mapping>
</web-app>`;
      fs.writeFileSync(path.join(webInfPath, 'web.xml'), webXmlContent);

      // Create Java file with annotations
      const javaContent = `package com.example;
import javax.servlet.annotation.WebServlet;

@WebServlet(urlPatterns = "/api/*")
public class ApiServlet {
}`;
      fs.writeFileSync(path.join(javaPath, 'ApiServlet.java'), javaContent);

      // Create static resources
      fs.writeFileSync(path.join(webappPath, 'css/style.css'), '/* css */');
      fs.writeFileSync(path.join(webappPath, 'index.jsp'), '<html></html>');

      const manifest = await discovery.discoverRoutes(projectPath);

      expect(manifest.routes.length).toBeGreaterThan(0);
      expect(manifest.metadata.sourceProject).toBe(projectPath);
      expect(manifest.metadata.timestamp).toBeDefined();
      expect(manifest.metadata.stats.total).toBeGreaterThan(0);

      // Should have routes from all sources
      const sources = new Set(manifest.routes.map(r => r.source));
      expect(sources.has('web.xml')).toBe(true);
      expect(sources.has('annotation')).toBe(true);
      expect(sources.has('filesystem')).toBe(true);
    });

    test('should handle missing directories gracefully', async () => {
      const projectPath = tempDir; // Empty directory
      
      const manifest = await discovery.discoverRoutes(projectPath);
      
      expect(manifest.routes).toHaveLength(0);
      expect(manifest.metadata.sourceProject).toBe(projectPath);
    });

    test('should respect includeStaticResources option', async () => {
      const projectPath = tempDir;
      const webappPath = path.join(projectPath, 'src/main/webapp');
      
      fs.mkdirSync(webappPath, { recursive: true });
      fs.writeFileSync(path.join(webappPath, 'style.css'), '/* css */');

      const discoveryWithoutStatic = new RouteDiscovery({
        includeStaticResources: false
      });

      const manifest = await discoveryWithoutStatic.discoverRoutes(projectPath);
      
      expect(manifest.routes.every(r => r.source !== 'static')).toBe(true);
    });
  });

  describe('postProcessRoutes', () => {
    test('should filter routes by minimum confidence', async () => {
      const discoveryWithMinConfidence = new RouteDiscovery({
        minConfidence: 0.8,
        includeStaticResources: false,
        includeWebXml: false,
        includeAnnotations: false
      });

      // Manually add routes with different confidence levels
      discoveryWithMinConfidence.manifestGenerator.addRoutes([
        { path: '/high', confidence: 0.9 },
        { path: '/medium', confidence: 0.7 },
        { path: '/low', confidence: 0.3 }
      ]);

      discoveryWithMinConfidence.postProcessRoutes();

      expect(discoveryWithMinConfidence.manifestGenerator.routes).toHaveLength(1);
      expect(discoveryWithMinConfidence.manifestGenerator.routes[0].path).toBe('/high');
    });

    test('should deduplicate routes when enabled', async () => {
      const discoveryWithDedup = new RouteDiscovery({
        deduplicateRoutes: true,
        includeStaticResources: false,
        includeWebXml: false,
        includeAnnotations: false
      });

      // Manually add duplicate routes
      discoveryWithDedup.manifestGenerator.addRoutes([
        { path: '/api/users', method: 'GET', confidence: 0.7 },
        { path: '/api/users', method: 'GET', confidence: 0.9 },
        { path: '/api/posts', method: 'GET', confidence: 0.8 }
      ]);

      discoveryWithDedup.postProcessRoutes();

      expect(discoveryWithDedup.manifestGenerator.routes).toHaveLength(2);
      const usersRoute = discoveryWithDedup.manifestGenerator.routes.find(r => r.path === '/api/users');
      expect(usersRoute.confidence).toBe(0.9);
    });
  });

  describe('getRoutes', () => {
    test('should return filtered routes', async () => {
      // Add some test routes
      discovery.manifestGenerator.addRoutes([
        { path: '/api/users', method: 'GET', type: 'controller' },
        { path: '/api/posts', method: 'POST', type: 'controller' },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet' }
      ]);

      const controllerRoutes = discovery.getRoutes({ type: 'controller' });
      expect(controllerRoutes).toHaveLength(2);
      expect(controllerRoutes.every(r => r.type === 'controller')).toBe(true);
    });
  });

  describe('analyzeForReactRouter', () => {
    test('should analyze routes for React Router patterns', () => {
      discovery.manifestGenerator.addRoutes([
        { path: '/users/:id', method: 'GET', type: 'controller' },
        { path: '/posts/123', method: 'GET', type: 'controller' },
        { path: '/admin/users/settings', method: 'GET', type: 'controller' },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet' },
        { path: '/api/v1/users', method: 'GET', type: 'controller' }
      ]);

      const analysis = discovery.analyzeForReactRouter();

      expect(analysis.dynamicRoutes.length).toBeGreaterThan(0);
      expect(analysis.staticRoutes.length).toBeGreaterThan(0);
      expect(analysis.nestedRoutes.length).toBeGreaterThan(0);
      expect(analysis.suggestions).toBeInstanceOf(Array);
    });
  });

  describe('exportManifest', () => {
    test('should export manifest to file', async () => {
      discovery.manifestGenerator.addRoutes([
        { path: '/api/test', method: 'GET', type: 'controller' }
      ]);

      const outputPath = path.join(tempDir, 'test-manifest.json');
      await discovery.exportManifest(outputPath, tempDir);

      expect(fs.existsSync(outputPath)).toBe(true);
      
      const content = fs.readFileSync(outputPath, 'utf8');
      const manifest = JSON.parse(content);
      
      expect(manifest.routes).toHaveLength(1);
      expect(manifest.routes[0].path).toBe('/api/test');
    });
  });

  describe('generateSummary', () => {
    test('should generate summary text', () => {
      discovery.manifestGenerator.addRoutes([
        { path: '/api/users', method: 'GET', type: 'controller' },
        { path: '/css/style.css', method: 'GET', type: 'stylesheet' }
      ]);

      const summary = discovery.generateSummary();
      
      expect(summary).toContain('Route Discovery Summary');
      expect(summary).toContain('Total Routes: 2');
      expect(typeof summary).toBe('string');
    });
  });
});
