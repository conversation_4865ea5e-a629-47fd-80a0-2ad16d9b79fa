{"name": "jsp2react-agent", "version": "1.0.0", "description": "JSP to React migration AI agent CLI tool", "main": "dist/index.js", "scripts": {"build": "tsc", "test": "jest", "test:watch": "jest --watch", "dev": "ts-node src/index.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["jsp", "react", "migration", "ai", "agent"], "author": "phodal", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"xml2js": "^0.6.2", "glob": "^10.3.0", "fast-xml-parser": "^4.3.0"}}